<!DOCTYPE html>
<html>

<head>
    <title>Form Validation</title>
    <script src="script.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>

<body>
    <div class="center">
        <form id="form" class="form" onsubmit="ValidateForm(event)">
            <h1>Form Validation</h1>
            <label for="" name>Name : </label>
            <input type="text" id="name" name="name" class="name" placeholder="Name"><br>
            <label for="" name>Phone No : </label>
            <input type="tel" id="phone" name="phone" placeholder="Phone No" required><br>
            <label for="" name>Email : </label>
            <input type="email" id="email" name="email" placeholder="Email" required><br>
            <label for="" name>Password : </label>
            <input type="password" id="pass" name="password" placeholder="Password" required><br>
            <button class="submit" type="submit">Submit</button>
        </form>
    </div>
</body>

</html>