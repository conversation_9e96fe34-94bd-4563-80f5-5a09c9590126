/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    line-height: 1.6;
    color: #333;
    min-height: 100vh;
}

/* Header and Navigation */
header {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    padding: 1rem 0;
}

nav li {
    margin: 0 2rem;
}

nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

nav a:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

/* Main Content */
main {
    margin-top: 80px;
    padding: 2rem 1rem;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    margin: 2rem auto;
    max-width: 800px;
    color: white;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Form Section */
.form-section {
    display: flex;
    justify-content: center;
    margin: 3rem 0;
}

.center {
    background-color: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

/* Typography */
h1, h2, h3, h4 {
    color: #333;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
    text-align: center;
}

h2 {
    font-size: 2rem;
    text-align: center;
    color: #007bff;
    margin-bottom: 2rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

/* Fieldsets */
fieldset {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    background-color: #f8f9fa;
}

legend {
    font-weight: bold;
    color: #007bff;
    padding: 0 1rem;
    font-size: 1.1rem;
}

/* Form Elements */
label {
    display: block;
    text-align: left;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.required {
    color: #dc3545;
    font-weight: bold;
}

input, select, textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background-color: white;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

input:invalid {
    border-color: #dc3545;
}

input:valid {
    border-color: #28a745;
}

small {
    color: #6c757d;
    font-size: 0.875rem;
    display: block;
    margin-bottom: 1rem;
}

/* Textarea */
textarea {
    resize: vertical;
    min-height: 100px;
}

/* Checkbox Group */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    margin: 1rem 0;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
    margin-top: 0.2rem;
}

.checkbox-group label {
    margin: 0;
    font-weight: normal;
    flex: 1;
}

/* Buttons */
.submit-section {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 120px;
}

.submit {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.submit:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.reset {
    background-color: #6c757d;
    color: white;
}

.reset:hover {
    background-color: #545b62;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

/* Login Link */
.login-link {
    text-align: center;
    margin-top: 1.5rem;
    color: #6c757d;
}

.login-link a {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
}

.login-link a:hover {
    text-decoration: underline;
}

/* About Section */
.about {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 3rem 2rem;
    margin: 3rem auto;
    max-width: 1000px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about h2 {
    text-align: center;
    color: #007bff;
    margin-bottom: 2rem;
}

.about article {
    margin-bottom: 2rem;
}

.about p {
    margin-bottom: 1rem;
    color: #495057;
    line-height: 1.7;
}

aside {
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    border-left: 4px solid #007bff;
}

aside h4 {
    color: #007bff;
    margin-bottom: 1rem;
}

aside ul {
    list-style: none;
    padding-left: 0;
}

aside li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    position: relative;
    padding-left: 1.5rem;
}

aside li:before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    position: absolute;
    left: 0;
}

aside li:last-child {
    border-bottom: none;
}

/* Help Section */
.help {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 3rem 2rem;
    margin: 3rem auto;
    max-width: 800px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.help h2 {
    text-align: center;
    color: #007bff;
    margin-bottom: 2rem;
}

details {
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

summary {
    background-color: #f8f9fa;
    padding: 1rem;
    cursor: pointer;
    font-weight: 600;
    color: #495057;
    transition: background-color 0.3s ease;
}

summary:hover {
    background-color: #e9ecef;
}

details[open] summary {
    background-color: #007bff;
    color: white;
}

details p {
    padding: 1rem;
    margin: 0;
    background-color: white;
    color: #495057;
    line-height: 1.6;
}

/* Footer */
footer {
    background-color: #343a40;
    color: white;
    padding: 3rem 2rem 2rem;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
}

.footer-content p {
    margin-bottom: 1rem;
}

address {
    font-style: normal;
    margin: 2rem 0;
    line-height: 1.8;
}

address a {
    color: #007bff;
    text-decoration: none;
}

address a:hover {
    text-decoration: underline;
}

.social-links {
    margin-top: 2rem;
}

.social-links a {
    color: white;
    text-decoration: none;
    margin: 0 1rem;
    padding: 0.5rem 1rem;
    border: 1px solid #6c757d;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: #007bff;
    border-color: #007bff;
    transform: translateY(-2px);
}

/* Progress Bar */
progress {
    width: 100%;
    height: 8px;
    position: fixed;
    top: 80px;
    left: 0;
    z-index: 999;
    appearance: none;
    border: none;
    background-color: rgba(255, 255, 255, 0.3);
}

progress::-webkit-progress-bar {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

progress::-webkit-progress-value {
    background: linear-gradient(90deg, #007bff, #28a745);
    border-radius: 4px;
}

progress::-moz-progress-bar {
    background: linear-gradient(90deg, #007bff, #28a745);
    border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    nav ul {
        flex-direction: column;
        padding: 0.5rem 0;
    }

    nav li {
        margin: 0.25rem 0;
    }

    .center {
        margin: 1rem;
        padding: 1.5rem;
    }

    .submit-section {
        flex-direction: column;
    }

    .about, .help {
        margin: 2rem 1rem;
        padding: 2rem 1rem;
    }

    aside {
        margin-top: 2rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 2rem 1rem;
        margin: 1rem;
    }

    .center {
        padding: 1rem;
    }

    fieldset {
        padding: 1rem;
    }

    .checkbox-group {
        flex-direction: column;
        gap: 0.25rem;
    }

    .checkbox-group input[type="checkbox"] {
        margin-top: 0;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.center, .about, .help {
    animation: fadeIn 0.6s ease-out;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for better accessibility */
input:focus, select:focus, textarea:focus, button:focus, summary:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    body {
        background: white;
        color: black;
    }

    .hero {
        background: black;
        color: white;
    }

    input, select, textarea {
        border: 2px solid black;
    }
}
